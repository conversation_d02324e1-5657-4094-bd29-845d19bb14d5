/**
 * UI组件库管理器
 * 提供组件浏览、搜索、分类和管理功能
 */
import React, { useState, useMemo } from 'react';
import {
  Card,
  Input,
  Select,
  Button,
  Space,
  Tabs,
  List,
  Avatar,
  Tag,
  Modal,
  Form,
  Upload,
  message,
  Tooltip,
  Popconfirm,
  Empty,
  Spin
} from 'antd';
import {
  SearchOutlined,
  AppstoreOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  DownloadOutlined,
  UploadOutlined,
  StarOutlined,
  StarFilled,
  EyeOutlined,
  CopyOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import './UIComponentLibrary.less';

const { Search } = Input;
const { Option } = Select;
const { TabPane } = Tabs;

// 组件分类
export enum ComponentCategory {
  BASIC = 'basic',
  FORM = 'form',
  NAVIGATION = 'navigation',
  FEEDBACK = 'feedback',
  LAYOUT = 'layout',
  DISPLAY = 'display',
  CUSTOM = 'custom'
}

// 组件数据接口
export interface ComponentData {
  id: string;
  name: string;
  description: string;
  category: ComponentCategory;
  tags: string[];
  thumbnail?: string;
  preview?: string;
  code: string;
  properties: Record<string, any>;
  dependencies?: string[];
  version: string;
  author: string;
  downloads: number;
  rating: number;
  isFavorite: boolean;
  isPublic: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// 组件库属性
interface UIComponentLibraryProps {
  onComponentSelect?: (component: ComponentData) => void;
  onComponentAdd?: (component: ComponentData) => void;
  showActions?: boolean;
  mode?: 'browse' | 'manage';
}

// 分类配置
const CATEGORY_CONFIG = {
  [ComponentCategory.BASIC]: {
    name: '基础组件',
    icon: <AppstoreOutlined />,
    color: '#1890ff'
  },
  [ComponentCategory.FORM]: {
    name: '表单组件',
    icon: <EditOutlined />,
    color: '#52c41a'
  },
  [ComponentCategory.NAVIGATION]: {
    name: '导航组件',
    icon: <AppstoreOutlined />,
    color: '#722ed1'
  },
  [ComponentCategory.FEEDBACK]: {
    name: '反馈组件',
    icon: <AppstoreOutlined />,
    color: '#fa8c16'
  },
  [ComponentCategory.LAYOUT]: {
    name: '布局组件',
    icon: <AppstoreOutlined />,
    color: '#13c2c2'
  },
  [ComponentCategory.DISPLAY]: {
    name: '展示组件',
    icon: <EyeOutlined />,
    color: '#eb2f96'
  },
  [ComponentCategory.CUSTOM]: {
    name: '自定义组件',
    icon: <StarOutlined />,
    color: '#faad14'
  }
};

// 模拟组件数据
const MOCK_COMPONENTS: ComponentData[] = [
  {
    id: '1',
    name: '基础按钮',
    description: '可自定义样式的基础按钮组件',
    category: ComponentCategory.BASIC,
    tags: ['按钮', '基础', '交互'],
    thumbnail: '/images/components/button.png',
    code: 'button',
    properties: {
      text: '按钮',
      type: 'primary',
      size: 'medium'
    },
    version: '1.0.0',
    author: 'DL Team',
    downloads: 1250,
    rating: 4.8,
    isFavorite: false,
    isPublic: true,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-15')
  },
  {
    id: '2',
    name: '输入框',
    description: '支持多种验证规则的输入框组件',
    category: ComponentCategory.FORM,
    tags: ['输入', '表单', '验证'],
    thumbnail: '/images/components/input.png',
    code: 'input',
    properties: {
      placeholder: '请输入内容',
      type: 'text',
      required: false
    },
    version: '1.2.0',
    author: 'DL Team',
    downloads: 980,
    rating: 4.6,
    isFavorite: true,
    isPublic: true,
    createdAt: new Date('2024-01-05'),
    updatedAt: new Date('2024-01-20')
  },
  {
    id: '3',
    name: '进度条',
    description: '动画效果丰富的进度条组件',
    category: ComponentCategory.FEEDBACK,
    tags: ['进度', '反馈', '动画'],
    thumbnail: '/images/components/progress.png',
    code: 'progressBar',
    properties: {
      value: 50,
      showText: true,
      animated: true
    },
    version: '1.1.0',
    author: 'DL Team',
    downloads: 756,
    rating: 4.7,
    isFavorite: false,
    isPublic: true,
    createdAt: new Date('2024-01-10'),
    updatedAt: new Date('2024-01-25')
  }
];

export const UIComponentLibrary: React.FC<UIComponentLibraryProps> = ({
  onComponentSelect,
  onComponentAdd,
  showActions = true,
  mode = 'browse'
}) => {
  const { t } = useTranslation();
  const [components, setComponents] = useState<ComponentData[]>(MOCK_COMPONENTS);
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<ComponentCategory | 'all'>('all');
  const [sortBy, setSortBy] = useState<'name' | 'downloads' | 'rating' | 'date'>('downloads');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showFavoritesOnly, setShowFavoritesOnly] = useState(false);
  
  // 模态框状态
  const [isCreateModalVisible, setIsCreateModalVisible] = useState(false);
  const [isEditModalVisible, setIsEditModalVisible] = useState(false);
  const [editingComponent, setEditingComponent] = useState<ComponentData>();
  const [previewComponent, setPreviewComponent] = useState<ComponentData>();
  
  const [form] = Form.useForm();

  // 过滤和排序组件
  const filteredComponents = useMemo(() => {
    let filtered = components.filter(component => {
      // 搜索过滤
      if (searchText) {
        const searchLower = searchText.toLowerCase();
        if (!component.name.toLowerCase().includes(searchLower) &&
            !component.description.toLowerCase().includes(searchLower) &&
            !component.tags.some(tag => tag.toLowerCase().includes(searchLower))) {
          return false;
        }
      }
      
      // 分类过滤
      if (selectedCategory !== 'all' && component.category !== selectedCategory) {
        return false;
      }
      
      // 收藏过滤
      if (showFavoritesOnly && !component.isFavorite) {
        return false;
      }
      
      return true;
    });

    // 排序
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'downloads':
          return b.downloads - a.downloads;
        case 'rating':
          return b.rating - a.rating;
        case 'date':
          return b.updatedAt.getTime() - a.updatedAt.getTime();
        default:
          return 0;
      }
    });

    return filtered;
  }, [components, searchText, selectedCategory, sortBy, showFavoritesOnly]);

  // 切换收藏状态
  const toggleFavorite = (componentId: string) => {
    setComponents(prev => prev.map(comp => 
      comp.id === componentId 
        ? { ...comp, isFavorite: !comp.isFavorite }
        : comp
    ));
  };

  // 删除组件
  const deleteComponent = (componentId: string) => {
    setComponents(prev => prev.filter(comp => comp.id !== componentId));
    message.success('组件已删除');
  };

  // 复制组件
  const duplicateComponent = (component: ComponentData) => {
    const newComponent: ComponentData = {
      ...component,
      id: `${component.id}_copy_${Date.now()}`,
      name: `${component.name} (副本)`,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    setComponents(prev => [...prev, newComponent]);
    message.success('组件已复制');
  };

  // 创建组件
  const handleCreateComponent = (values: any) => {
    const newComponent: ComponentData = {
      id: `component_${Date.now()}`,
      name: values.name,
      description: values.description,
      category: values.category,
      tags: values.tags ? values.tags.split(',').map((tag: string) => tag.trim()) : [],
      code: values.code,
      properties: {},
      version: '1.0.0',
      author: 'User',
      downloads: 0,
      rating: 0,
      isFavorite: false,
      isPublic: values.isPublic,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    setComponents(prev => [...prev, newComponent]);
    setIsCreateModalVisible(false);
    form.resetFields();
    message.success('组件创建成功');
  };

  // 编辑组件
  const handleEditComponent = (values: any) => {
    if (!editingComponent) return;
    
    const updatedComponent: ComponentData = {
      ...editingComponent,
      name: values.name,
      description: values.description,
      category: values.category,
      tags: values.tags ? values.tags.split(',').map((tag: string) => tag.trim()) : [],
      isPublic: values.isPublic,
      updatedAt: new Date()
    };
    
    setComponents(prev => prev.map(comp => 
      comp.id === editingComponent.id ? updatedComponent : comp
    ));
    setIsEditModalVisible(false);
    setEditingComponent(undefined);
    form.resetFields();
    message.success('组件更新成功');
  };

  // 渲染组件卡片
  const renderComponentCard = (component: ComponentData) => (
    <Card
      key={component.id}
      className="component-card"
      cover={
        component.thumbnail ? (
          <img alt={component.name} src={component.thumbnail} />
        ) : (
          <div className="component-placeholder">
            {CATEGORY_CONFIG[component.category].icon}
          </div>
        )
      }
      actions={showActions ? [
        <Tooltip title="预览">
          <EyeOutlined onClick={() => setPreviewComponent(component)} />
        </Tooltip>,
        <Tooltip title={component.isFavorite ? '取消收藏' : '收藏'}>
          {component.isFavorite ? (
            <StarFilled onClick={() => toggleFavorite(component.id)} />
          ) : (
            <StarOutlined onClick={() => toggleFavorite(component.id)} />
          )}
        </Tooltip>,
        mode === 'manage' ? (
          <Tooltip title="编辑">
            <EditOutlined onClick={() => {
              setEditingComponent(component);
              form.setFieldsValue({
                name: component.name,
                description: component.description,
                category: component.category,
                tags: component.tags.join(', '),
                isPublic: component.isPublic
              });
              setIsEditModalVisible(true);
            }} />
          </Tooltip>
        ) : (
          <Tooltip title="复制">
            <CopyOutlined onClick={() => duplicateComponent(component)} />
          </Tooltip>
        ),
        mode === 'manage' ? (
          <Popconfirm
            title="确定要删除这个组件吗？"
            onConfirm={() => deleteComponent(component.id)}
          >
            <DeleteOutlined />
          </Popconfirm>
        ) : (
          <Tooltip title="下载">
            <DownloadOutlined onClick={() => message.info('下载功能开发中')} />
          </Tooltip>
        )
      ] : undefined}
      onClick={() => onComponentSelect?.(component)}
    >
      <Card.Meta
        title={
          <div className="component-title">
            <span>{component.name}</span>
            <Tag color={CATEGORY_CONFIG[component.category].color}>
              {CATEGORY_CONFIG[component.category].name}
            </Tag>
          </div>
        }
        description={
          <div className="component-description">
            <p>{component.description}</p>
            <div className="component-meta">
              <span>下载: {component.downloads}</span>
              <span>评分: {component.rating}</span>
              <span>版本: {component.version}</span>
            </div>
            <div className="component-tags">
              {component.tags.map(tag => (
                <Tag key={tag} size="small">{tag}</Tag>
              ))}
            </div>
          </div>
        }
      />
    </Card>
  );

  // 渲染列表项
  const renderListItem = (component: ComponentData) => (
    <List.Item
      key={component.id}
      actions={showActions ? [
        <Button
          type="text"
          icon={<EyeOutlined />}
          onClick={() => setPreviewComponent(component)}
        />,
        <Button
          type="text"
          icon={component.isFavorite ? <StarFilled /> : <StarOutlined />}
          onClick={() => toggleFavorite(component.id)}
        />,
        mode === 'manage' ? (
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => {
              setEditingComponent(component);
              form.setFieldsValue({
                name: component.name,
                description: component.description,
                category: component.category,
                tags: component.tags.join(', '),
                isPublic: component.isPublic
              });
              setIsEditModalVisible(true);
            }}
          />
        ) : (
          <Button
            type="text"
            icon={<CopyOutlined />}
            onClick={() => duplicateComponent(component)}
          />
        ),
        mode === 'manage' ? (
          <Popconfirm
            title="确定要删除这个组件吗？"
            onConfirm={() => deleteComponent(component.id)}
          >
            <Button type="text" icon={<DeleteOutlined />} danger />
          </Popconfirm>
        ) : (
          <Button
            type="text"
            icon={<DownloadOutlined />}
            onClick={() => message.info('下载功能开发中')}
          />
        )
      ] : undefined}
      onClick={() => onComponentSelect?.(component)}
    >
      <List.Item.Meta
        avatar={
          <Avatar
            src={component.thumbnail}
            icon={CATEGORY_CONFIG[component.category].icon}
            style={{ backgroundColor: CATEGORY_CONFIG[component.category].color }}
          />
        }
        title={
          <div className="list-item-title">
            <span>{component.name}</span>
            <Tag color={CATEGORY_CONFIG[component.category].color}>
              {CATEGORY_CONFIG[component.category].name}
            </Tag>
          </div>
        }
        description={
          <div>
            <p>{component.description}</p>
            <div className="list-item-meta">
              <span>下载: {component.downloads}</span>
              <span>评分: {component.rating}</span>
              <span>版本: {component.version}</span>
            </div>
            <div className="list-item-tags">
              {component.tags.map(tag => (
                <Tag key={tag} size="small">{tag}</Tag>
              ))}
            </div>
          </div>
        }
      />
    </List.Item>
  );

  return (
    <div className="ui-component-library">
      {/* 工具栏 */}
      <div className="library-toolbar">
        <div className="toolbar-left">
          <Search
            placeholder="搜索组件..."
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            style={{ width: 300 }}
            prefix={<SearchOutlined />}
          />
          <Select
            value={selectedCategory}
            onChange={setSelectedCategory}
            style={{ width: 150 }}
          >
            <Option value="all">全部分类</Option>
            {Object.entries(CATEGORY_CONFIG).map(([key, config]) => (
              <Option key={key} value={key}>
                {config.name}
              </Option>
            ))}
          </Select>
          <Select
            value={sortBy}
            onChange={setSortBy}
            style={{ width: 120 }}
          >
            <Option value="downloads">下载量</Option>
            <Option value="rating">评分</Option>
            <Option value="name">名称</Option>
            <Option value="date">更新时间</Option>
          </Select>
        </div>
        
        <div className="toolbar-right">
          <Space>
            <Button
              type={showFavoritesOnly ? 'primary' : 'default'}
              icon={<StarOutlined />}
              onClick={() => setShowFavoritesOnly(!showFavoritesOnly)}
            >
              收藏
            </Button>
            {mode === 'manage' && (
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => setIsCreateModalVisible(true)}
              >
                创建组件
              </Button>
            )}
          </Space>
        </div>
      </div>

      {/* 组件列表 */}
      <div className="library-content">
        <Spin spinning={loading}>
          {filteredComponents.length === 0 ? (
            <Empty description="没有找到匹配的组件" />
          ) : viewMode === 'grid' ? (
            <div className="component-grid">
              {filteredComponents.map(renderComponentCard)}
            </div>
          ) : (
            <List
              className="component-list"
              dataSource={filteredComponents}
              renderItem={renderListItem}
            />
          )}
        </Spin>
      </div>

      {/* 创建组件模态框 */}
      <Modal
        title="创建新组件"
        open={isCreateModalVisible}
        onOk={() => form.submit()}
        onCancel={() => {
          setIsCreateModalVisible(false);
          form.resetFields();
        }}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleCreateComponent}
        >
          <Form.Item
            name="name"
            label="组件名称"
            rules={[{ required: true, message: '请输入组件名称' }]}
          >
            <Input placeholder="输入组件名称" />
          </Form.Item>
          
          <Form.Item
            name="description"
            label="组件描述"
            rules={[{ required: true, message: '请输入组件描述' }]}
          >
            <Input.TextArea rows={3} placeholder="输入组件描述" />
          </Form.Item>
          
          <Form.Item
            name="category"
            label="组件分类"
            rules={[{ required: true, message: '请选择组件分类' }]}
          >
            <Select placeholder="选择组件分类">
              {Object.entries(CATEGORY_CONFIG).map(([key, config]) => (
                <Option key={key} value={key}>
                  {config.name}
                </Option>
              ))}
            </Select>
          </Form.Item>
          
          <Form.Item
            name="tags"
            label="标签"
            help="多个标签用逗号分隔"
          >
            <Input placeholder="输入标签，用逗号分隔" />
          </Form.Item>
          
          <Form.Item
            name="code"
            label="组件代码"
            rules={[{ required: true, message: '请输入组件代码' }]}
          >
            <Input.TextArea rows={6} placeholder="输入组件代码" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 编辑组件模态框 */}
      <Modal
        title="编辑组件"
        open={isEditModalVisible}
        onOk={() => form.submit()}
        onCancel={() => {
          setIsEditModalVisible(false);
          setEditingComponent(undefined);
          form.resetFields();
        }}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleEditComponent}
        >
          <Form.Item
            name="name"
            label="组件名称"
            rules={[{ required: true, message: '请输入组件名称' }]}
          >
            <Input placeholder="输入组件名称" />
          </Form.Item>
          
          <Form.Item
            name="description"
            label="组件描述"
            rules={[{ required: true, message: '请输入组件描述' }]}
          >
            <Input.TextArea rows={3} placeholder="输入组件描述" />
          </Form.Item>
          
          <Form.Item
            name="category"
            label="组件分类"
            rules={[{ required: true, message: '请选择组件分类' }]}
          >
            <Select placeholder="选择组件分类">
              {Object.entries(CATEGORY_CONFIG).map(([key, config]) => (
                <Option key={key} value={key}>
                  {config.name}
                </Option>
              ))}
            </Select>
          </Form.Item>
          
          <Form.Item
            name="tags"
            label="标签"
            help="多个标签用逗号分隔"
          >
            <Input placeholder="输入标签，用逗号分隔" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 预览模态框 */}
      <Modal
        title={`预览: ${previewComponent?.name}`}
        open={!!previewComponent}
        onCancel={() => setPreviewComponent(undefined)}
        footer={[
          <Button key="close" onClick={() => setPreviewComponent(undefined)}>
            关闭
          </Button>,
          <Button
            key="add"
            type="primary"
            onClick={() => {
              if (previewComponent && onComponentAdd) {
                onComponentAdd(previewComponent);
                setPreviewComponent(undefined);
              }
            }}
          >
            添加到设计
          </Button>
        ]}
        width={800}
      >
        {previewComponent && (
          <div className="component-preview">
            <div className="preview-info">
              <h4>组件信息</h4>
              <p><strong>描述:</strong> {previewComponent.description}</p>
              <p><strong>分类:</strong> {CATEGORY_CONFIG[previewComponent.category].name}</p>
              <p><strong>版本:</strong> {previewComponent.version}</p>
              <p><strong>作者:</strong> {previewComponent.author}</p>
              <div className="preview-tags">
                {previewComponent.tags.map(tag => (
                  <Tag key={tag}>{tag}</Tag>
                ))}
              </div>
            </div>
            <div className="preview-demo">
              <h4>预览效果</h4>
              <div className="demo-container">
                {/* 这里可以渲染组件的实际预览 */}
                <p>组件预览功能开发中...</p>
              </div>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default UIComponentLibrary;
