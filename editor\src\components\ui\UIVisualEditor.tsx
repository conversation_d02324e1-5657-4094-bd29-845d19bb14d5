/**
 * UI可视化编辑器
 * 支持拖拽式UI设计
 */
import React, { useState, useRef, useCallback, useEffect } from 'react';
import { DndProvider, useDrag, useDrop } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import {
  Card,
  Button,
  Space,
  Tooltip,
  Divider,
  message,
  Modal,
  Input,
  Select,
  Row,
  Col,
  Slider,
  ColorPicker
} from 'antd';
import {
  AppstoreOutlined,
  DragOutlined,
  EditOutlined,
  DeleteOutlined,
  CopyOutlined,
  UndoOutlined,
  RedoOutlined,
  SaveOutlined,
  EyeOutlined,
  SettingOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import engineService from '../../services/EngineService';
import './UIVisualEditor.less';

const { Option } = Select;

// UI组件类型
export enum UIElementType {
  BUTTON = 'button',
  TEXT = 'text',
  IMAGE = 'image',
  INPUT = 'input',
  PANEL = 'panel',
  PROGRESS_BAR = 'progressBar',
  TOOLTIP = 'tooltip',
  MODAL = 'modal'
}

// UI元素数据接口
export interface UIElementData {
  id: string;
  type: UIElementType;
  name: string;
  x: number;
  y: number;
  width: number;
  height: number;
  properties: Record<string, any>;
  children?: UIElementData[];
}

// 拖拽项目类型
const ItemTypes = {
  UI_ELEMENT: 'ui_element',
  COMPONENT: 'component'
};

// 组件库项目
interface ComponentLibraryItem {
  type: UIElementType;
  name: string;
  icon: React.ReactNode;
  defaultProps: Record<string, any>;
}

const COMPONENT_LIBRARY: ComponentLibraryItem[] = [
  {
    type: UIElementType.BUTTON,
    name: '按钮',
    icon: <AppstoreOutlined />,
    defaultProps: {
      text: '按钮',
      backgroundColor: '#1890ff',
      color: '#ffffff',
      borderRadius: 4,
      padding: '8px 16px'
    }
  },
  {
    type: UIElementType.TEXT,
    name: '文本',
    icon: <EditOutlined />,
    defaultProps: {
      text: '文本内容',
      fontSize: 14,
      color: '#000000'
    }
  },
  {
    type: UIElementType.INPUT,
    name: '输入框',
    icon: <EditOutlined />,
    defaultProps: {
      placeholder: '请输入内容',
      borderColor: '#d9d9d9',
      borderRadius: 4,
      padding: '8px 12px'
    }
  },
  {
    type: UIElementType.PANEL,
    name: '面板',
    icon: <AppstoreOutlined />,
    defaultProps: {
      backgroundColor: '#ffffff',
      borderColor: '#d9d9d9',
      borderRadius: 8,
      padding: 16
    }
  },
  {
    type: UIElementType.PROGRESS_BAR,
    name: '进度条',
    icon: <AppstoreOutlined />,
    defaultProps: {
      value: 50,
      progressColor: '#1890ff',
      trackColor: '#f0f0f0'
    }
  }
];

// 可拖拽组件项
const DraggableComponentItem: React.FC<{
  item: ComponentLibraryItem;
}> = ({ item }) => {
  const [{ isDragging }, drag] = useDrag({
    type: ItemTypes.COMPONENT,
    item: { type: item.type, defaultProps: item.defaultProps },
    collect: (monitor) => ({
      isDragging: monitor.isDragging()
    })
  });

  return (
    <div
      ref={drag}
      className={`component-item ${isDragging ? 'dragging' : ''}`}
      style={{ opacity: isDragging ? 0.5 : 1 }}
    >
      <div className="component-icon">{item.icon}</div>
      <div className="component-name">{item.name}</div>
    </div>
  );
};

// 可拖拽UI元素
const DraggableUIElement: React.FC<{
  element: UIElementData;
  isSelected: boolean;
  onSelect: (element: UIElementData) => void;
  onUpdate: (element: UIElementData) => void;
  onDelete: (id: string) => void;
}> = ({ element, isSelected, onSelect, onUpdate, onDelete }) => {
  const [{ isDragging }, drag] = useDrag({
    type: ItemTypes.UI_ELEMENT,
    item: { id: element.id },
    collect: (monitor) => ({
      isDragging: monitor.isDragging()
    })
  });

  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onSelect(element);
  };

  const handleDoubleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    // 双击编辑
  };

  const renderElement = () => {
    const style: React.CSSProperties = {
      position: 'absolute',
      left: element.x,
      top: element.y,
      width: element.width,
      height: element.height,
      border: isSelected ? '2px solid #1890ff' : '1px solid transparent',
      cursor: 'move',
      ...element.properties
    };

    switch (element.type) {
      case UIElementType.BUTTON:
        return (
          <button style={style} onClick={handleClick} onDoubleClick={handleDoubleClick}>
            {element.properties.text || '按钮'}
          </button>
        );
      case UIElementType.TEXT:
        return (
          <div style={style} onClick={handleClick} onDoubleClick={handleDoubleClick}>
            {element.properties.text || '文本'}
          </div>
        );
      case UIElementType.INPUT:
        return (
          <input
            style={style}
            placeholder={element.properties.placeholder}
            onClick={handleClick}
            onDoubleClick={handleDoubleClick}
          />
        );
      case UIElementType.PANEL:
        return (
          <div style={style} onClick={handleClick} onDoubleClick={handleDoubleClick}>
            {element.children?.map(child => (
              <DraggableUIElement
                key={child.id}
                element={child}
                isSelected={false}
                onSelect={onSelect}
                onUpdate={onUpdate}
                onDelete={onDelete}
              />
            ))}
          </div>
        );
      default:
        return (
          <div style={style} onClick={handleClick} onDoubleClick={handleDoubleClick}>
            {element.type}
          </div>
        );
    }
  };

  return (
    <div ref={drag} style={{ opacity: isDragging ? 0.5 : 1 }}>
      {renderElement()}
      {isSelected && (
        <div className="element-controls">
          <Button size="small" icon={<EditOutlined />} />
          <Button size="small" icon={<CopyOutlined />} />
          <Button size="small" icon={<DeleteOutlined />} onClick={() => onDelete(element.id)} />
        </div>
      )}
    </div>
  );
};

// 设计画布
const DesignCanvas: React.FC<{
  elements: UIElementData[];
  selectedElement?: UIElementData;
  onElementSelect: (element: UIElementData) => void;
  onElementUpdate: (element: UIElementData) => void;
  onElementDelete: (id: string) => void;
  onElementAdd: (element: UIElementData) => void;
}> = ({ elements, selectedElement, onElementSelect, onElementUpdate, onElementDelete, onElementAdd }) => {
  const canvasRef = useRef<HTMLDivElement>(null);

  const [{ isOver }, drop] = useDrop({
    accept: [ItemTypes.COMPONENT, ItemTypes.UI_ELEMENT],
    drop: (item: any, monitor) => {
      const offset = monitor.getClientOffset();
      const canvasRect = canvasRef.current?.getBoundingClientRect();
      
      if (offset && canvasRect) {
        const x = offset.x - canvasRect.left;
        const y = offset.y - canvasRect.top;

        if (item.type && item.defaultProps) {
          // 从组件库拖拽
          const newElement: UIElementData = {
            id: `element_${Date.now()}`,
            type: item.type,
            name: `${item.type}_${Date.now()}`,
            x,
            y,
            width: 100,
            height: 32,
            properties: item.defaultProps
          };
          onElementAdd(newElement);
        } else if (item.id) {
          // 移动现有元素
          const element = elements.find(el => el.id === item.id);
          if (element) {
            onElementUpdate({
              ...element,
              x,
              y
            });
          }
        }
      }
    },
    collect: (monitor) => ({
      isOver: monitor.isOver()
    })
  });

  const handleCanvasClick = () => {
    onElementSelect(null as any);
  };

  return (
    <div
      ref={(node) => {
        drop(node);
        canvasRef.current = node;
      }}
      className={`design-canvas ${isOver ? 'drag-over' : ''}`}
      onClick={handleCanvasClick}
    >
      {elements.map(element => (
        <DraggableUIElement
          key={element.id}
          element={element}
          isSelected={selectedElement?.id === element.id}
          onSelect={onElementSelect}
          onUpdate={onElementUpdate}
          onDelete={onElementDelete}
        />
      ))}
    </div>
  );
};

// 属性编辑面板
const PropertyPanel: React.FC<{
  element?: UIElementData;
  onUpdate: (element: UIElementData) => void;
}> = ({ element, onUpdate }) => {
  const { t: _ } = useTranslation();

  if (!element) {
    return (
      <div className="property-panel">
        <div className="no-selection">请选择一个元素</div>
      </div>
    );
  }

  const handlePropertyChange = (key: string, value: any) => {
    onUpdate({
      ...element,
      properties: {
        ...element.properties,
        [key]: value
      }
    });
  };

  const handlePositionChange = (key: 'x' | 'y' | 'width' | 'height', value: number) => {
    onUpdate({
      ...element,
      [key]: value
    });
  };

  return (
    <div className="property-panel">
      <h3>属性编辑</h3>
      
      {/* 基本属性 */}
      <div className="property-section">
        <h4>基本属性</h4>
        <Row gutter={[8, 8]}>
          <Col span={12}>
            <label>名称</label>
            <Input
              value={element.name}
              onChange={(e) => onUpdate({ ...element, name: e.target.value })}
            />
          </Col>
          <Col span={12}>
            <label>类型</label>
            <Input value={element.type} disabled />
          </Col>
        </Row>
      </div>

      {/* 位置和尺寸 */}
      <div className="property-section">
        <h4>位置和尺寸</h4>
        <Row gutter={[8, 8]}>
          <Col span={12}>
            <label>X</label>
            <Input
              type="number"
              value={element.x}
              onChange={(e) => handlePositionChange('x', Number(e.target.value))}
            />
          </Col>
          <Col span={12}>
            <label>Y</label>
            <Input
              type="number"
              value={element.y}
              onChange={(e) => handlePositionChange('y', Number(e.target.value))}
            />
          </Col>
          <Col span={12}>
            <label>宽度</label>
            <Input
              type="number"
              value={element.width}
              onChange={(e) => handlePositionChange('width', Number(e.target.value))}
            />
          </Col>
          <Col span={12}>
            <label>高度</label>
            <Input
              type="number"
              value={element.height}
              onChange={(e) => handlePositionChange('height', Number(e.target.value))}
            />
          </Col>
        </Row>
      </div>

      {/* 样式属性 */}
      <div className="property-section">
        <h4>样式属性</h4>
        {element.type === UIElementType.BUTTON && (
          <>
            <Row gutter={[8, 8]}>
              <Col span={24}>
                <label>文本</label>
                <Input
                  value={element.properties.text}
                  onChange={(e) => handlePropertyChange('text', e.target.value)}
                />
              </Col>
              <Col span={12}>
                <label>背景色</label>
                <ColorPicker
                  value={element.properties.backgroundColor}
                  onChange={(color) => handlePropertyChange('backgroundColor', color.toHexString())}
                />
              </Col>
              <Col span={12}>
                <label>文字颜色</label>
                <ColorPicker
                  value={element.properties.color}
                  onChange={(color) => handlePropertyChange('color', color.toHexString())}
                />
              </Col>
            </Row>
          </>
        )}
        
        {element.type === UIElementType.TEXT && (
          <>
            <Row gutter={[8, 8]}>
              <Col span={24}>
                <label>文本内容</label>
                <Input
                  value={element.properties.text}
                  onChange={(e) => handlePropertyChange('text', e.target.value)}
                />
              </Col>
              <Col span={12}>
                <label>字体大小</label>
                <Slider
                  min={12}
                  max={48}
                  value={element.properties.fontSize}
                  onChange={(value) => handlePropertyChange('fontSize', value)}
                />
              </Col>
              <Col span={12}>
                <label>文字颜色</label>
                <ColorPicker
                  value={element.properties.color}
                  onChange={(color) => handlePropertyChange('color', color.toHexString())}
                />
              </Col>
            </Row>
          </>
        )}

        {element.type === UIElementType.PROGRESS_BAR && (
          <>
            <Row gutter={[8, 8]}>
              <Col span={24}>
                <label>进度值</label>
                <Slider
                  min={0}
                  max={100}
                  value={element.properties.value}
                  onChange={(value) => handlePropertyChange('value', value)}
                />
              </Col>
              <Col span={12}>
                <label>进度条颜色</label>
                <ColorPicker
                  value={element.properties.progressColor}
                  onChange={(color) => handlePropertyChange('progressColor', color.toHexString())}
                />
              </Col>
              <Col span={12}>
                <label>轨道颜色</label>
                <ColorPicker
                  value={element.properties.trackColor}
                  onChange={(color) => handlePropertyChange('trackColor', color.toHexString())}
                />
              </Col>
            </Row>
          </>
        )}
      </div>
    </div>
  );
};

// 主编辑器组件
export const UIVisualEditor: React.FC = () => {
  const [elements, setElements] = useState<UIElementData[]>([]);
  const [selectedElement, setSelectedElement] = useState<UIElementData>();
  const [history, setHistory] = useState<UIElementData[][]>([[]]);
  const [historyIndex, setHistoryIndex] = useState(0);

  // 添加元素
  const handleElementAdd = useCallback((element: UIElementData) => {
    const newElements = [...elements, element];
    setElements(newElements);
    setSelectedElement(element);
    
    // 添加到历史记录
    const newHistory = history.slice(0, historyIndex + 1);
    newHistory.push(newElements);
    setHistory(newHistory);
    setHistoryIndex(newHistory.length - 1);
  }, [elements, history, historyIndex]);

  // 更新元素
  const handleElementUpdate = useCallback((updatedElement: UIElementData) => {
    const newElements = elements.map(el => 
      el.id === updatedElement.id ? updatedElement : el
    );
    setElements(newElements);
    setSelectedElement(updatedElement);
    
    // 添加到历史记录
    const newHistory = history.slice(0, historyIndex + 1);
    newHistory.push(newElements);
    setHistory(newHistory);
    setHistoryIndex(newHistory.length - 1);
  }, [elements, history, historyIndex]);

  // 删除元素
  const handleElementDelete = useCallback((id: string) => {
    const newElements = elements.filter(el => el.id !== id);
    setElements(newElements);
    setSelectedElement(undefined);
    
    // 添加到历史记录
    const newHistory = history.slice(0, historyIndex + 1);
    newHistory.push(newElements);
    setHistory(newHistory);
    setHistoryIndex(newHistory.length - 1);
  }, [elements, history, historyIndex]);

  // 撤销
  const handleUndo = useCallback(() => {
    if (historyIndex > 0) {
      const newIndex = historyIndex - 1;
      setElements(history[newIndex]);
      setHistoryIndex(newIndex);
      setSelectedElement(undefined);
    }
  }, [history, historyIndex]);

  // 重做
  const handleRedo = useCallback(() => {
    if (historyIndex < history.length - 1) {
      const newIndex = historyIndex + 1;
      setElements(history[newIndex]);
      setHistoryIndex(newIndex);
      setSelectedElement(undefined);
    }
  }, [history, historyIndex]);

  // 保存设计
  const handleSave = useCallback(() => {
    // 这里可以保存到服务器或本地存储
    message.success('设计已保存');
  }, []);

  // 预览
  const handlePreview = useCallback(() => {
    // 这里可以打开预览窗口
    message.info('预览功能开发中');
  }, []);

  return (
    <DndProvider backend={HTML5Backend}>
      <div className="ui-visual-editor">
        {/* 工具栏 */}
        <div className="toolbar">
          <Space>
            <Tooltip title="撤销">
              <Button 
                icon={<UndoOutlined />} 
                onClick={handleUndo}
                disabled={historyIndex <= 0}
              />
            </Tooltip>
            <Tooltip title="重做">
              <Button 
                icon={<RedoOutlined />} 
                onClick={handleRedo}
                disabled={historyIndex >= history.length - 1}
              />
            </Tooltip>
            <Divider type="vertical" />
            <Tooltip title="保存">
              <Button icon={<SaveOutlined />} onClick={handleSave} />
            </Tooltip>
            <Tooltip title="预览">
              <Button icon={<EyeOutlined />} onClick={handlePreview} />
            </Tooltip>
          </Space>
        </div>

        <div className="editor-content">
          {/* 组件库 */}
          <div className="component-library">
            <h3>组件库</h3>
            <div className="component-list">
              {COMPONENT_LIBRARY.map(item => (
                <DraggableComponentItem key={item.type} item={item} />
              ))}
            </div>
          </div>

          {/* 设计画布 */}
          <div className="canvas-container">
            <DesignCanvas
              elements={elements}
              selectedElement={selectedElement}
              onElementSelect={setSelectedElement}
              onElementUpdate={handleElementUpdate}
              onElementDelete={handleElementDelete}
              onElementAdd={handleElementAdd}
            />
          </div>

          {/* 属性面板 */}
          <div className="property-container">
            <PropertyPanel
              element={selectedElement}
              onUpdate={handleElementUpdate}
            />
          </div>
        </div>
      </div>
    </DndProvider>
  );
};

export default UIVisualEditor;
